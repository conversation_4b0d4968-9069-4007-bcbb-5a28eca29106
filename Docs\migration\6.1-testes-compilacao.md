# 6.1 Testes de Compilação

## Objetivo
Executar testes abrangentes de compilação para garantir que todo o código fonte compile corretamente com Java 21 e todas as dependências estejam funcionando.

## Pré-requisitos
- Otimizações de performance implementadas (5.4)
- Código fonte migrado (Fase 5)
- Configurações de build atualizadas (4.4)

## Tempo Estimado
3-5 dias

## Estratégia de Testes de Compilação

### **1. Compilação Incremental**

#### **Teste por Módulos:**
```batch
@echo off
echo ========================================
echo    TESTES DE COMPILAÇÃO INCREMENTAL
echo ========================================
echo.

set SOURCE_DIR=Source\WEB-INF\src
set CLASSES_DIR=Source\WEB-INF\classes
set LIB_DIR=Source\WEB-INF\lib

echo === LIMPANDO COMPILAÇÃO ANTERIOR ===
rmdir /s /q "%CLASSES_DIR%" 2>nul
mkdir "%CLASSES_DIR%"

echo.
echo === TESTANDO COMPILAÇÃO POR PACOTES ===

REM Compilar pacotes core primeiro
echo Compilando pacotes core...
javac -cp "%LIB_DIR%\*" -d "%CLASSES_DIR%" -encoding UTF-8 -source 21 -target 21 ^
    "%SOURCE_DIR%\com\visionnaire\webpublication\core\*.java" ^
    "%SOURCE_DIR%\com\visionnaire\webpublication\util\*.java"

if %errorlevel% neq 0 (
    echo ❌ ERRO: Compilação dos pacotes core falhou
    goto :error
)
echo ✅ Pacotes core compilados com sucesso

REM Compilar pacotes de negócio
echo Compilando pacotes de negócio...
javac -cp "%LIB_DIR%\*;%CLASSES_DIR%" -d "%CLASSES_DIR%" -encoding UTF-8 -source 21 -target 21 ^
    "%SOURCE_DIR%\com\visionnaire\webpublication\business\*.java" ^
    "%SOURCE_DIR%\com\visionnaire\webpublication\service\*.java"

if %errorlevel% neq 0 (
    echo ❌ ERRO: Compilação dos pacotes de negócio falhou
    goto :error
)
echo ✅ Pacotes de negócio compilados com sucesso

REM Compilar pacotes web
echo Compilando pacotes web...
javac -cp "%LIB_DIR%\*;%CLASSES_DIR%" -d "%CLASSES_DIR%" -encoding UTF-8 -source 21 -target 21 ^
    "%SOURCE_DIR%\com\visionnaire\webpublication\servlet\*.java" ^
    "%SOURCE_DIR%\com\visionnaire\webpublication\filter\*.java" ^
    "%SOURCE_DIR%\com\visionnaire\webpublication\listener\*.java"

if %errorlevel% neq 0 (
    echo ❌ ERRO: Compilação dos pacotes web falhou
    goto :error
)
echo ✅ Pacotes web compilados com sucesso

echo.
echo ✅ COMPILAÇÃO INCREMENTAL CONCLUÍDA COM SUCESSO!
goto :end

:error
echo.
echo ❌ COMPILAÇÃO FALHOU - Verifique os erros acima
exit /b 1

:end
pause
```

### **2. Compilação Completa com Ant**

#### **test_full_compilation.xml:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project name="CompilationTests" default="test-all" basedir=".">
    
    <description>
        Testes abrangentes de compilação para Java 21
    </description>
    
    <!-- Load properties -->
    <property file="build.properties"/>
    
    <!-- Compilation settings -->
    <property name="compile.debug" value="true"/>
    <property name="compile.deprecation" value="true"/>
    <property name="compile.encoding" value="UTF-8"/>
    <property name="compile.source" value="21"/>
    <property name="compile.target" value="21"/>
    
    <!-- Paths -->
    <property name="src.dir" value="WEB-INF/src"/>
    <property name="classes.dir" value="WEB-INF/classes"/>
    <property name="lib.dir" value="WEB-INF/lib"/>
    <property name="test.classes.dir" value="temp/test-classes"/>
    <property name="reports.dir" value="reports/compilation"/>
    
    <!-- Classpath -->
    <path id="compile.classpath">
        <fileset dir="${lib.dir}">
            <include name="**/*.jar"/>
        </fileset>
        <fileset dir="${tomcat.home}/lib">
            <include name="servlet-api.jar"/>
            <include name="jsp-api.jar"/>
            <include name="el-api.jar"/>
        </fileset>
    </path>
    
    <!-- Clean -->
    <target name="clean" description="Clean compilation artifacts">
        <delete dir="${classes.dir}" quiet="true"/>
        <delete dir="${test.classes.dir}" quiet="true"/>
        <delete dir="${reports.dir}" quiet="true"/>
        <mkdir dir="${classes.dir}"/>
        <mkdir dir="${test.classes.dir}"/>
        <mkdir dir="${reports.dir}"/>
    </target>
    
    <!-- Test Java 21 compatibility -->
    <target name="test-java21" description="Test Java 21 compatibility">
        <echo message="Testing Java 21 compatibility..."/>
        
        <!-- Verify Java version -->
        <condition property="java21.available">
            <contains string="${java.version}" substring="21"/>
        </condition>
        <fail unless="java21.available" message="Java 21 is required"/>
        
        <!-- Test compilation with Java 21 features -->
        <javac srcdir="${src.dir}"
               destdir="${test.classes.dir}"
               classpathref="compile.classpath"
               debug="${compile.debug}"
               deprecation="${compile.deprecation}"
               encoding="${compile.encoding}"
               source="${compile.source}"
               target="${compile.target}"
               includeantruntime="false"
               fork="true"
               memorymaximumsize="2048m"
               failonerror="true">
            
            <!-- Java 21 specific compiler arguments -->
            <compilerarg value="-Xlint:unchecked"/>
            <compilerarg value="-Xlint:deprecation"/>
            <compilerarg value="-parameters"/>
            <compilerarg value="--enable-preview" if="java.enable.preview"/>
            
            <!-- Module system arguments -->
            <compilerarg value="--add-modules" if="java.modules"/>
            <compilerarg value="ALL-SYSTEM" if="java.modules"/>
        </javac>
        
        <echo message="✅ Java 21 compilation successful"/>
    </target>
    
    <!-- Test dependencies -->
    <target name="test-dependencies" description="Test all dependencies">
        <echo message="Testing dependency compilation..."/>
        
        <!-- Create test class to verify all dependencies -->
        <echo file="${test.classes.dir}/DependencyTest.java"><![CDATA[
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.mail.Session;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;

public class DependencyTest extends HttpServlet {
    private static final Logger logger = LogManager.getLogger(DependencyTest.class);
    private final ObjectMapper mapper = new ObjectMapper();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) {
        LocalDateTime now = LocalDateTime.now();
        String formatted = now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        logger.info("Test successful: {}", formatted);
        
        CompletableFuture.supplyAsync(() -> "async test")
            .thenAccept(result -> logger.info("Async result: {}", result));
    }
}
        ]]></echo>
        
        <!-- Compile test class -->
        <javac srcdir="${test.classes.dir}"
               destdir="${test.classes.dir}"
               classpathref="compile.classpath"
               source="${compile.source}"
               target="${compile.target}"
               encoding="${compile.encoding}"
               includeantruntime="false"/>
        
        <echo message="✅ Dependencies compilation successful"/>
    </target>
    
    <!-- Test warnings -->
    <target name="test-warnings" description="Test compilation warnings">
        <echo message="Testing for compilation warnings..."/>
        
        <javac srcdir="${src.dir}"
               destdir="${test.classes.dir}"
               classpathref="compile.classpath"
               debug="${compile.debug}"
               deprecation="true"
               encoding="${compile.encoding}"
               source="${compile.source}"
               target="${compile.target}"
               includeantruntime="false"
               fork="true"
               memorymaximumsize="2048m">
            
            <!-- Capture warnings -->
            <compilerarg value="-Xlint:all"/>
            <compilerarg value="-Werror" if="fail.on.warnings"/>
        </javac>
        
        <echo message="✅ Warning analysis completed"/>
    </target>
    
    <!-- Test with different JVM options -->
    <target name="test-jvm-options" description="Test with different JVM options">
        <echo message="Testing with various JVM options..."/>
        
        <!-- Test with module system -->
        <javac srcdir="${src.dir}"
               destdir="${test.classes.dir}"
               classpathref="compile.classpath"
               source="${compile.source}"
               target="${compile.target}"
               encoding="${compile.encoding}"
               includeantruntime="false"
               fork="true">
            
            <compilerarg value="--add-opens"/>
            <compilerarg value="java.base/java.lang=ALL-UNNAMED"/>
            <compilerarg value="--add-opens"/>
            <compilerarg value="java.base/java.util=ALL-UNNAMED"/>
        </javac>
        
        <echo message="✅ JVM options test successful"/>
    </target>
    
    <!-- Generate compilation report -->
    <target name="generate-report" description="Generate compilation report">
        <echo message="Generating compilation report..."/>
        
        <!-- Count compiled classes -->
        <resourcecount property="compiled.classes.count">
            <fileset dir="${classes.dir}" includes="**/*.class"/>
        </resourcecount>
        
        <!-- Count source files -->
        <resourcecount property="source.files.count">
            <fileset dir="${src.dir}" includes="**/*.java"/>
        </resourcecount>
        
        <!-- Generate report -->
        <echo file="${reports.dir}/compilation-report.txt">COMPILATION REPORT
==================
Date: ${DSTAMP} ${TSTAMP}
Java Version: ${java.version}
Ant Version: ${ant.version}

STATISTICS:
- Source files: ${source.files.count}
- Compiled classes: ${compiled.classes.count}
- Compilation target: Java ${compile.target}
- Encoding: ${compile.encoding}

CLASSPATH:
${toString:compile.classpath}

STATUS: SUCCESS
</echo>
        
        <echo message="✅ Report generated: ${reports.dir}/compilation-report.txt"/>
    </target>
    
    <!-- Main test target -->
    <target name="test-all" depends="clean,test-java21,test-dependencies,test-warnings,test-jvm-options,generate-report" 
            description="Run all compilation tests">
        <echo message=""/>
        <echo message="========================================"/>
        <echo message="   ALL COMPILATION TESTS PASSED!"/>
        <echo message="========================================"/>
        <echo message="Source files compiled: ${source.files.count}"/>
        <echo message="Classes generated: ${compiled.classes.count}"/>
        <echo message="Java version: ${java.version}"/>
        <echo message="Report: ${reports.dir}/compilation-report.txt"/>
        <echo message="========================================"/>
    </target>
    
</project>
```

### **3. Script de Validação de Classpath**

#### **validate_classpath.ps1:**
```powershell
# Script para validar classpath e dependências
Write-Host "=== VALIDAÇÃO DE CLASSPATH E DEPENDÊNCIAS ===" -ForegroundColor Yellow

$libDir = "Source\WEB-INF\lib"
$classesDir = "Source\WEB-INF\classes"
$sourceDir = "Source\WEB-INF\src"

# Verificar estrutura de diretórios
Write-Host ""
Write-Host "=== VERIFICANDO ESTRUTURA ===" -ForegroundColor Cyan

$directories = @($libDir, $classesDir, $sourceDir)
foreach ($dir in $directories) {
    if (Test-Path $dir) {
        $fileCount = (Get-ChildItem $dir -Recurse -File).Count
        Write-Host "✅ $dir ($fileCount arquivos)" -ForegroundColor Green
    } else {
        Write-Host "❌ $dir - FALTANDO" -ForegroundColor Red
    }
}

# Verificar JARs essenciais
Write-Host ""
Write-Host "=== VERIFICANDO JARS ESSENCIAIS ===" -ForegroundColor Cyan

$essentialJars = @{
    "Jakarta Servlet" = "jakarta.servlet-api*.jar"
    "Jakarta JSP" = "jakarta.servlet.jsp-api*.jar"
    "JSTL" = "jakarta.servlet.jsp.jstl*.jar"
    "Oracle JDBC" = "ojdbc11*.jar"
    "MySQL JDBC" = "mysql-connector-j*.jar"
    "Log4j Core" = "log4j-core*.jar"
    "Log4j API" = "log4j-api*.jar"
    "Jackson Core" = "jackson-core*.jar"
    "Jackson Databind" = "jackson-databind*.jar"
    "Commons Lang3" = "commons-lang3*.jar"
    "Gson" = "gson*.jar"
    "PSS Framework" = "pss.jar"
}

$missingJars = @()
foreach ($jar in $essentialJars.Keys) {
    $pattern = $essentialJars[$jar]
    $found = Get-ChildItem "$libDir\$pattern" -ErrorAction SilentlyContinue
    
    if ($found) {
        Write-Host "✅ $jar`: $($found.Name)" -ForegroundColor Green
    } else {
        Write-Host "❌ $jar`: $pattern - NÃO ENCONTRADO" -ForegroundColor Red
        $missingJars += $jar
    }
}

# Verificar conflitos de versão
Write-Host ""
Write-Host "=== VERIFICANDO CONFLITOS DE VERSÃO ===" -ForegroundColor Cyan

$allJars = Get-ChildItem "$libDir\*.jar" | ForEach-Object { $_.Name }
$duplicates = $allJars | Group-Object { ($_ -split '-')[0] } | Where-Object { $_.Count -gt 1 }

if ($duplicates) {
    Write-Host "⚠️ Possíveis conflitos encontrados:" -ForegroundColor Yellow
    foreach ($dup in $duplicates) {
        Write-Host "  - $($dup.Name): $($dup.Group -join ', ')" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ Nenhum conflito de versão detectado" -ForegroundColor Green
}

# Testar classpath
Write-Host ""
Write-Host "=== TESTANDO CLASSPATH ===" -ForegroundColor Cyan

$classpath = (Get-ChildItem "$libDir\*.jar" | ForEach-Object { $_.FullName }) -join ";"

# Criar classe de teste
$testClass = @"
import jakarta.servlet.http.HttpServlet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;

public class ClasspathTest {
    private static final Logger logger = LogManager.getLogger(ClasspathTest.class);
    
    public static void main(String[] args) {
        System.out.println("Testing classpath...");
        
        try {
            // Test Jakarta Servlet
            Class.forName("jakarta.servlet.http.HttpServlet");
            System.out.println("✅ Jakarta Servlet API available");
            
            // Test Log4j
            Logger testLogger = LogManager.getLogger("test");
            testLogger.info("Log4j test");
            System.out.println("✅ Log4j available");
            
            // Test Jackson
            ObjectMapper mapper = new ObjectMapper();
            System.out.println("✅ Jackson available");
            
            // Test Java Time
            LocalDateTime now = LocalDateTime.now();
            System.out.println("✅ Java Time API available: " + now);
            
            System.out.println("✅ All classpath tests passed!");
            
        } catch (Exception e) {
            System.err.println("❌ Classpath test failed: " + e.getMessage());
            System.exit(1);
        }
    }
}
"@

# Salvar e compilar classe de teste
$testFile = "temp\ClasspathTest.java"
New-Item -ItemType Directory -Force -Path "temp" | Out-Null
$testClass | Out-File -FilePath $testFile -Encoding UTF8

try {
    # Compilar
    $compileResult = javac -cp $classpath -d temp $testFile 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Compilação de teste bem-sucedida" -ForegroundColor Green
        
        # Executar
        $runResult = java -cp "$classpath;temp" ClasspathTest 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Execução de teste bem-sucedida" -ForegroundColor Green
            Write-Host $runResult -ForegroundColor White
        } else {
            Write-Host "❌ Execução de teste falhou" -ForegroundColor Red
            Write-Host $runResult -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Compilação de teste falhou" -ForegroundColor Red
        Write-Host $compileResult -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Erro ao executar teste de classpath: $($_.Exception.Message)" -ForegroundColor Red
}

# Relatório final
Write-Host ""
Write-Host "=== RELATÓRIO FINAL ===" -ForegroundColor Yellow

if ($missingJars.Count -eq 0) {
    Write-Host "✅ Todas as dependências essenciais estão presentes" -ForegroundColor Green
} else {
    Write-Host "❌ $($missingJars.Count) dependências faltando:" -ForegroundColor Red
    foreach ($jar in $missingJars) {
        Write-Host "  - $jar" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Total de JARs: $($allJars.Count)" -ForegroundColor Cyan
Write-Host "Tamanho total: $([math]::Round((Get-ChildItem $libDir\*.jar | Measure-Object Length -Sum).Sum / 1MB, 2)) MB" -ForegroundColor Cyan

# Limpeza
Remove-Item "temp\ClasspathTest.*" -ErrorAction SilentlyContinue
```

### **4. Teste de Compatibilidade de Versões**

#### **test_version_compatibility.java:**
```java
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.List;

public class TestVersionCompatibility {
    
    public static void main(String[] args) {
        System.out.println("=== TESTE DE COMPATIBILIDADE DE VERSÕES ===");
        System.out.println();
        
        testJavaVersion();
        testJVMArguments();
        testModuleSystem();
        testCompilerCompliance();
    }
    
    private static void testJavaVersion() {
        System.out.println("=== VERSÃO DO JAVA ===");
        
        String javaVersion = System.getProperty("java.version");
        String javaVendor = System.getProperty("java.vendor");
        String javaHome = System.getProperty("java.home");
        
        System.out.println("Java Version: " + javaVersion);
        System.out.println("Java Vendor: " + javaVendor);
        System.out.println("Java Home: " + javaHome);
        
        // Verificar se é Java 21
        if (javaVersion.startsWith("21")) {
            System.out.println("✅ Java 21 detectado corretamente");
        } else {
            System.out.println("❌ Java 21 não detectado - versão atual: " + javaVersion);
        }
        
        // Verificar runtime version
        Runtime.Version runtimeVersion = Runtime.version();
        System.out.println("Runtime Version: " + runtimeVersion);
        System.out.println("Feature Version: " + runtimeVersion.feature());
        
        if (runtimeVersion.feature() >= 21) {
            System.out.println("✅ Runtime compatível com Java 21");
        } else {
            System.out.println("❌ Runtime incompatível - requer Java 21+");
        }
        
        System.out.println();
    }
    
    private static void testJVMArguments() {
        System.out.println("=== ARGUMENTOS DA JVM ===");
        
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        List<String> jvmArgs = runtimeBean.getInputArguments();
        
        System.out.println("Argumentos da JVM:");
        for (String arg : jvmArgs) {
            System.out.println("  " + arg);
        }
        
        // Verificar argumentos importantes
        boolean hasModuleOpens = jvmArgs.stream()
            .anyMatch(arg -> arg.contains("--add-opens"));
        
        if (hasModuleOpens) {
            System.out.println("✅ Argumentos de module system detectados");
        } else {
            System.out.println("⚠️ Nenhum argumento --add-opens detectado");
        }
        
        System.out.println();
    }
    
    private static void testModuleSystem() {
        System.out.println("=== SISTEMA DE MÓDULOS ===");
        
        // Verificar se estamos em um módulo
        Module currentModule = TestVersionCompatibility.class.getModule();
        System.out.println("Módulo atual: " + currentModule.getName());
        System.out.println("É nomeado: " + currentModule.isNamed());
        
        // Testar acesso a pacotes
        try {
            // Tentar acessar classe interna (deve funcionar com --add-opens)
            Class<?> unsafeClass = Class.forName("sun.misc.Unsafe");
            System.out.println("⚠️ Acesso a sun.misc.Unsafe ainda possível");
        } catch (ClassNotFoundException e) {
            System.out.println("✅ sun.misc.Unsafe não acessível (esperado)");
        } catch (Exception e) {
            System.out.println("ℹ️ Erro ao acessar sun.misc.Unsafe: " + e.getMessage());
        }
        
        // Testar reflection
        try {
            java.lang.reflect.Field field = String.class.getDeclaredField("value");
            if (field.trySetAccessible()) {
                System.out.println("✅ Reflection com trySetAccessible() funcionando");
            } else {
                System.out.println("⚠️ Reflection bloqueada pelo module system");
            }
        } catch (Exception e) {
            System.out.println("❌ Erro na reflection: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testCompilerCompliance() {
        System.out.println("=== COMPLIANCE DO COMPILADOR ===");
        
        // Verificar propriedades do compilador
        String specVersion = System.getProperty("java.specification.version");
        String vmVersion = System.getProperty("java.vm.version");
        String vmName = System.getProperty("java.vm.name");
        
        System.out.println("Specification Version: " + specVersion);
        System.out.println("VM Version: " + vmVersion);
        System.out.println("VM Name: " + vmName);
        
        // Testar features do Java 21
        testJava21Features();
        
        System.out.println();
    }
    
    private static void testJava21Features() {
        System.out.println("=== FEATURES DO JAVA 21 ===");
        
        // Test Virtual Threads
        try {
            Thread virtualThread = Thread.ofVirtual().start(() -> {
                System.out.println("✅ Virtual Threads funcionando");
            });
            virtualThread.join();
        } catch (Exception e) {
            System.out.println("❌ Virtual Threads não funcionando: " + e.getMessage());
        }
        
        // Test Pattern Matching (basic)
        Object testObj = "Hello World";
        String result = switch (testObj) {
            case String s -> "String: " + s.length();
            case Integer i -> "Integer: " + i;
            default -> "Other";
        };
        System.out.println("✅ Pattern Matching funcionando: " + result);
        
        // Test Sequenced Collections
        try {
            java.util.SequencedSet<String> sequencedSet = new java.util.LinkedHashSet<>();
            sequencedSet.addFirst("first");
            sequencedSet.addLast("last");
            System.out.println("✅ Sequenced Collections funcionando");
        } catch (Exception e) {
            System.out.println("❌ Sequenced Collections não funcionando: " + e.getMessage());
        }
        
        // Test Record Patterns (if available)
        try {
            record Point(int x, int y) {}
            Point p = new Point(1, 2);
            
            String pointDesc = switch (p) {
                case Point(var x, var y) -> "Point at (" + x + ", " + y + ")";
            };
            System.out.println("✅ Record Patterns funcionando: " + pointDesc);
        } catch (Exception e) {
            System.out.println("⚠️ Record Patterns podem não estar disponíveis: " + e.getMessage());
        }
    }
}
```

### **5. Script de Teste Automatizado**

#### **run_compilation_tests.bat:**
```batch
@echo off
echo ========================================
echo    SUITE COMPLETA DE TESTES DE COMPILAÇÃO
echo ========================================
echo.

set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

echo === VERIFICANDO AMBIENTE ===
java -version
echo.

echo === VALIDANDO CLASSPATH ===
powershell -ExecutionPolicy Bypass -File "validate_classpath.ps1"
if %errorlevel% neq 0 (
    echo ❌ Validação de classpath falhou
    goto :error
)

echo.
echo === TESTE DE COMPATIBILIDADE ===
javac -cp "Source\WEB-INF\lib\*" -d temp TestVersionCompatibility.java
if %errorlevel% neq 0 (
    echo ❌ Compilação do teste de compatibilidade falhou
    goto :error
)

java -cp "Source\WEB-INF\lib\*;temp" TestVersionCompatibility
if %errorlevel% neq 0 (
    echo ❌ Teste de compatibilidade falhou
    goto :error
)

echo.
echo === COMPILAÇÃO INCREMENTAL ===
call test_incremental_compilation.bat
if %errorlevel% neq 0 (
    echo ❌ Compilação incremental falhou
    goto :error
)

echo.
echo === COMPILAÇÃO COMPLETA COM ANT ===
ant -f test_full_compilation.xml test-all
if %errorlevel% neq 0 (
    echo ❌ Compilação completa com Ant falhou
    goto :error
)

echo.
echo === TESTE DE BUILD FINAL ===
ant clean compile
if %errorlevel% neq 0 (
    echo ❌ Build final falhou
    goto :error
)

echo.
echo ========================================
echo ✅ TODOS OS TESTES DE COMPILAÇÃO PASSARAM!
echo ========================================
echo.
echo Próximos passos:
echo 1. Executar testes unitários
echo 2. Executar testes de integração
echo 3. Validar funcionalidades críticas
echo 4. Executar testes de performance
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ TESTES DE COMPILAÇÃO FALHARAM!
echo ========================================
echo.
echo Verifique os erros acima e corrija antes de prosseguir.
echo.
exit /b 1

:end
pause
```

## Checklist de Validação

### **Compilação Básica:**
- [ ] Código compila sem erros com Java 21
- [ ] Todas as dependências resolvidas
- [ ] Classpath configurado corretamente
- [ ] Encoding UTF-8 funcionando

### **Compatibilidade:**
- [ ] Jakarta EE APIs funcionando
- [ ] Log4j 2.x funcionando
- [ ] Java Time API funcionando
- [ ] Modern collections funcionando

### **Features Java 21:**
- [ ] Virtual Threads disponíveis
- [ ] Pattern Matching funcionando
- [ ] Sequenced Collections funcionando
- [ ] Record Patterns (se habilitado)

### **Build System:**
- [ ] Ant build funcionando
- [ ] Compilação incremental funcionando
- [ ] WAR generation funcionando
- [ ] Reports sendo gerados

### **Performance:**
- [ ] Tempo de compilação aceitável
- [ ] Uso de memória controlado
- [ ] Paralelização funcionando
- [ ] Cache de compilação efetivo

## Próximo Passo
**[6.2 Testes Unitários](./6.2-testes-unitarios.md)**

---
**Status**: ⏳ Pendente
**Responsável**: Desenvolvedor/QA
**Estimativa**: 3-5 dias
