@echo off
echo ========================================
echo   TESTE DE COMPATIBILIDADE PSS + JAVA 21
echo ========================================
echo.

echo === VERIFICANDO AMBIENTE ===
java -version
if %errorlevel% neq 0 (
    echo ? Java n�o encontrado
    goto :end
)

echo.
echo === VERIFICANDO BIBLIOTECAS PSS ===
if exist "Source\WEB-INF\lib\pss.jar" (
    echo ? pss.jar encontrado
) else (
    echo ? pss.jar n�o encontrado
    goto :end
)

if exist "Source\WEB-INF\lib\acss-local.jar" (
    echo ? acss-local.jar encontrado
) else (
    echo ? acss-local.jar n�o encontrado
)

echo.
echo === TESTE DE COMPILA��O B�SICA ===
mkdir temp_test 2>nul
cd temp_test

echo Criando classe de teste...
echo package test; > TestPSS.java
echo import com.visionnaire.PSS.pid; >> TestPSS.java
echo import com.visionnaire.PSS.client.StorageObjectBase; >> TestPSS.java
echo public class TestPSS extends StorageObjectBase { >> TestPSS.java
echo     protected pid klass; >> TestPSS.java
echo     private String testField; >> TestPSS.java
echo. >> TestPSS.java
echo     public TestPSS(pid oid) { >> TestPSS.java
echo         // Constructor with pid >> TestPSS.java
echo     } >> TestPSS.java
echo. >> TestPSS.java
echo     public TestPSS() { >> TestPSS.java
echo         // Default constructor >> TestPSS.java
echo     } >> TestPSS.java
echo. >> TestPSS.java
echo     protected String pssMappedClassName() { >> TestPSS.java
echo         return "test.TestPSS"; >> TestPSS.java
echo     } >> TestPSS.java
echo. >> TestPSS.java
echo     public pid getClassId() { >> TestPSS.java
echo         return klass; >> TestPSS.java
echo     } >> TestPSS.java
echo. >> TestPSS.java
echo     protected int pssMarshallSize() { >> TestPSS.java
echo         return 3; >> TestPSS.java
echo     } >> TestPSS.java
echo. >> TestPSS.java
echo     public void pssMarshall(Object[] attribs) { >> TestPSS.java
echo         attribs[0] = pssId; >> TestPSS.java
echo         attribs[1] = klass; >> TestPSS.java
echo         attribs[2] = testField; >> TestPSS.java
echo     } >> TestPSS.java
echo. >> TestPSS.java
echo     public void pssUnmarshall(Object[] attribs) { >> TestPSS.java
echo         klass = (pid)attribs[1]; >> TestPSS.java
echo         testField = (String)attribs[2]; >> TestPSS.java
echo     } >> TestPSS.java
echo. >> TestPSS.java
echo     public String getTestField() { >> TestPSS.java
echo         return testField; >> TestPSS.java
echo     } >> TestPSS.java
echo. >> TestPSS.java
echo     public void setTestField(String testField) { >> TestPSS.java
echo         this.testField = testField; >> TestPSS.java
echo     } >> TestPSS.java
echo } >> TestPSS.java

echo.
echo Tentando compilar com PSS...
javac -cp "..\Source\WEB-INF\lib\*" TestPSS.java
if %errorlevel% equ 0 (
    echo ? COMPILA��O B�SICA: SUCESSO
    set COMPILE_OK=1
) else (
    echo ? COMPILA��O B�SICA: FALHOU
    set COMPILE_OK=0
)

echo.
echo === TESTE DE EXECU��O ===
if %COMPILE_OK% equ 1 (
    echo Testando carregamento de classes...
    java -cp "..\Source\WEB-INF\lib\*;." test.TestPSS
    if %errorlevel% equ 0 (
        echo ? EXECU��O: SUCESSO
    ) else (
        echo ? EXECU��O: FALHOU
    )
) else (
    echo ?? Pulando teste de execu��o (compila��o falhou)
)

echo.
echo === AN�LISE DE DEPEND�NCIAS INTERNAS ===
echo Verificando uso de APIs internas...
jdeps --jdk-internals ..\Source\WEB-INF\lib\pss.jar 2>nul
if %errorlevel% equ 0 (
    echo ?? PSS usa APIs internas do JDK
) else (
    echo ? Nenhuma API interna detectada
)

cd ..
rmdir /s /q temp_test 2>nul

echo.
echo ========================================
echo   RESULTADO DO TESTE
echo ========================================
if %COMPILE_OK% equ 1 (
    echo ? PSS parece compat�vel com Java 21
    echo ?? Testes mais extensivos s�o recomendados
) else (
    echo ? PSS tem problemas de compatibilidade
    echo ?? Contate a Visionnaire para vers�o atualizada
)

:end
echo.
pause
