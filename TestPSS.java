package test;

import com.visionnaire.PSS.pid;
import com.visionnaire.PSS.client.StorageObjectBase;

public class TestPSS extends StorageObjectBase {
    protected pid klass;
    private String testField;

    public TestPSS(pid oid) {
        // Constructor with pid
        this.klass = oid;
    }

    public TestPSS() {
        // Default constructor
    }

    @Override
    protected String pssMappedClassName() {
        return "test.TestPSS";
    }

    @Override
    public pid getClassId() {
        return klass;
    }

    @Override
    protected int pssMarshallSize() {
        return 3;
    }

    @Override
    public void pssMarshall(Object[] attribs) {
        if (attribs.length >= 3) {
            attribs[0] = pssId;
            attribs[1] = klass;
            attribs[2] = testField;
        }
    }

    @Override
    public void pssUnmarshall(Object[] attribs) {
        if (attribs.length >= 3) {
            // pssId is handled by parent class
            klass = (pid)attribs[1];
            testField = (String)attribs[2];
        }
    }

    public String getTestField() {
        return testField;
    }

    public void setTestField(String testField) {
        this.testField = testField;
    }

    // Método de teste estático
    public static void main(String[] args) {
        try {
            System.out.println("Testando carregamento de classes PSS...");
            
            // Teste 1: Carregar classe StorageObjectBase
            Class.forName("com.visionnaire.PSS.client.StorageObjectBase");
            System.out.println("✅ StorageObjectBase carregado com sucesso");
            
            // Teste 2: Carregar classe pid
            Class.forName("com.visionnaire.PSS.pid");
            System.out.println("✅ pid carregado com sucesso");
            
            // Teste 3: Instanciar TestPSS
            TestPSS test = new TestPSS();
            test.setTestField("Teste Java 21");
            System.out.println("✅ TestPSS instanciado com sucesso");
            System.out.println("✅ Campo de teste: " + test.getTestField());
            
            // Teste 4: Testar serialização
            Object[] attribs = new Object[3];
            test.pssMarshall(attribs);
            System.out.println("✅ pssMarshall executado com sucesso");
            
            TestPSS test2 = new TestPSS();
            test2.pssUnmarshall(attribs);
            System.out.println("✅ pssUnmarshall executado com sucesso");
            
            System.out.println("");
            System.out.println("🎉 TODOS OS TESTES BÁSICOS PASSARAM!");
            System.out.println("✅ PSS parece compatível com Java 21");
            
        } catch (ClassNotFoundException e) {
            System.out.println("❌ Erro ao carregar classe PSS: " + e.getMessage());
            System.exit(1);
        } catch (Exception e) {
            System.out.println("❌ Erro durante teste: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}