# Script para Corrigir Dependências PSS
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "   CORREÇÃO DE DEPENDÊNCIAS PSS" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
Write-Host ""

$libDir = "Source\WEB-INF\lib"

# Verificar se diretório lib existe
if (-not (Test-Path $libDir)) {
    Write-Host "❌ Diretório $libDir não encontrado" -ForegroundColor Red
    exit 1
}

Write-Host "=== VERIFICANDO DEPENDÊNCIAS ATUAIS ===" -ForegroundColor Cyan

# Verificar dependências críticas
$criticalJars = @{
    "servlet-api" = "javax.servlet-api*.jar"
    "log4j" = "log4j*.jar"
    "pss" = "pss.jar"
    "vutil" = "vutil.jar"
    "vjdbc" = "vjdbc.jar"
}

foreach ($dep in $criticalJars.GetEnumerator()) {
    $found = Get-ChildItem "$libDir\$($dep.Value)" -ErrorAction SilentlyContinue
    if ($found) {
        Write-Host "✅ $($dep.Key): $($found.Name)" -ForegroundColor Green
    } else {
        Write-Host "❌ $($dep.Key): NÃO ENCONTRADO" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== TENTANDO CORRIGIR DEPENDÊNCIAS ===" -ForegroundColor Cyan

# 1. Tentar executar ant RefreshDepends
Write-Host ""
Write-Host "1. Executando 'ant RefreshDepends'..." -ForegroundColor Yellow
try {
    Set-Location Source
    $antResult = ant RefreshDepends 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ ant RefreshDepends executado com sucesso" -ForegroundColor Green
    } else {
        Write-Host "⚠️ ant RefreshDepends falhou:" -ForegroundColor Yellow
        Write-Host $antResult -ForegroundColor Gray
    }
    Set-Location ..
} catch {
    Write-Host "❌ Erro ao executar ant: $($_.Exception.Message)" -ForegroundColor Red
    Set-Location ..
}

# 2. Verificar se servlet-api está presente
Write-Host ""
Write-Host "2. Verificando Servlet API..." -ForegroundColor Yellow
$servletApi = Get-ChildItem "$libDir\*servlet*.jar" -ErrorAction SilentlyContinue
if (-not $servletApi) {
    Write-Host "⚠️ Servlet API não encontrada" -ForegroundColor Yellow
    Write-Host "   Recomendação: Adicionar javax.servlet-api-4.0.1.jar" -ForegroundColor Gray
    
    # Oferecer download automático
    $download = Read-Host "Deseja baixar javax.servlet-api-4.0.1.jar automaticamente? (s/n)"
    if ($download -eq "s" -or $download -eq "S") {
        try {
            $url = "https://repo1.maven.org/maven2/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar"
            $output = "$libDir\javax.servlet-api-4.0.1.jar"
            Write-Host "   Baixando servlet API..." -ForegroundColor Cyan
            Invoke-WebRequest -Uri $url -OutFile $output
            Write-Host "✅ javax.servlet-api-4.0.1.jar baixado com sucesso" -ForegroundColor Green
        } catch {
            Write-Host "❌ Erro ao baixar servlet API: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} else {
    Write-Host "✅ Servlet API encontrada: $($servletApi.Name)" -ForegroundColor Green
}

# 3. Verificar dependências Visionnaire
Write-Host ""
Write-Host "3. Verificando dependências Visionnaire..." -ForegroundColor Yellow

$visionnaireJars = @("vutil.jar", "vjdbc.jar", "vlog.jar", "vtask.jar", "viter.jar")
$missingVisionnaire = @()

foreach ($jar in $visionnaireJars) {
    if (-not (Test-Path "$libDir\$jar")) {
        $missingVisionnaire += $jar
    }
}

if ($missingVisionnaire.Count -gt 0) {
    Write-Host "⚠️ Dependências Visionnaire ausentes:" -ForegroundColor Yellow
    foreach ($jar in $missingVisionnaire) {
        Write-Host "   ❌ $jar" -ForegroundColor Red
    }
    Write-Host ""
    Write-Host "   📋 AÇÕES NECESSÁRIAS:" -ForegroundColor Cyan
    Write-Host "   1. Verificar ant.properties (caminhos vcomp.lib)" -ForegroundColor White
    Write-Host "   2. Verificar se diretório V:/Visionnaire/... existe" -ForegroundColor White
    Write-Host "   3. Contatar equipe Visionnaire para obter JARs" -ForegroundColor White
} else {
    Write-Host "✅ Todas as dependências Visionnaire encontradas" -ForegroundColor Green
}

# 4. Re-testar após correções
Write-Host ""
Write-Host "4. Re-testando compatibilidade..." -ForegroundColor Yellow
Write-Host "   Executando teste de compatibilidade novamente..." -ForegroundColor Cyan

try {
    $testResult = .\test_pss_compatibility.ps1 2>&1
    # Não mostrar toda a saída, apenas o resultado final
    if ($testResult -match "PSS COMPATÍVEL COM JAVA 21") {
        Write-Host "🎉 SUCESSO: PSS agora compatível!" -ForegroundColor Green
    } elseif ($testResult -match "DEPENDÊNCIAS AUSENTES") {
        Write-Host "⚠️ Ainda há dependências ausentes" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Problemas de compatibilidade persistem" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Erro ao executar teste: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "   RESUMO DAS CORREÇÕES" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow

Write-Host ""
Write-Host "📋 PRÓXIMOS PASSOS:" -ForegroundColor Cyan
Write-Host "1. Se ainda há dependências ausentes:" -ForegroundColor White
Write-Host "   - Contatar Visionnaire para obter bibliotecas faltantes" -ForegroundColor Gray
Write-Host "   - Verificar configuração do ambiente de desenvolvimento" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Se servlet API foi adicionada:" -ForegroundColor White
Write-Host "   - Re-executar: .\test_pss_compatibility.ps1" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Para análise detalhada:" -ForegroundColor White
Write-Host "   - Consultar: Docs\migration\PSS_DEPENDENCY_ANALYSIS.md" -ForegroundColor Gray
Write-Host ""

Read-Host "Pressione Enter para continuar"
