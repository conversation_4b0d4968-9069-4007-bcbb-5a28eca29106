﻿# PowerShell Script para Teste de Compatibilidade PSS + Java 21
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "   TESTE DE COMPATIBILIDADE PSS + JAVA 21" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
Write-Host ""

# Verificar ambiente Java
Write-Host "=== VERIFICANDO AMBIENTE ===" -ForegroundColor Cyan
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✅ Java encontrado: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java não encontrado" -ForegroundColor Red
    exit 1
}

# Verificar bibliotecas PSS
Write-Host ""
Write-Host "=== VERIFICANDO BIBLIOTECAS PSS ===" -ForegroundColor Cyan

$pssJar = "Source\WEB-INF\lib\pss.jar"
$acssJar = "Source\WEB-INF\lib\acss-local.jar"

if (Test-Path $pssJar) {
    Write-Host "✅ pss.jar encontrado" -ForegroundColor Green
} else {
    Write-Host "❌ pss.jar não encontrado em $pssJar" -ForegroundColor Red
    exit 1
}

if (Test-Path $acssJar) {
    Write-Host "✅ acss-local.jar encontrado" -ForegroundColor Green
} else {
    Write-Host "⚠️ acss-local.jar não encontrado" -ForegroundColor Yellow
}

# Criar diretório de teste
Write-Host ""
Write-Host "=== PREPARANDO TESTE ===" -ForegroundColor Cyan
$testDir = "temp_pss_test"
if (Test-Path $testDir) {
    Remove-Item $testDir -Recurse -Force
}
New-Item -ItemType Directory -Path $testDir | Out-Null
Set-Location $testDir

# Criar classe de teste Java
Write-Host "Criando classe de teste Java..."
$javaCode = @"
package test;

import com.visionnaire.PSS.pid;
import com.visionnaire.PSS.client.StorageObjectBase;

public class TestPSS extends StorageObjectBase {
    protected pid klass;
    private String testField;

    public TestPSS(pid oid) {
        // Constructor with pid
        this.klass = oid;
    }

    public TestPSS() {
        // Default constructor
    }

    @Override
    protected String pssMappedClassName() {
        return "test.TestPSS";
    }

    @Override
    public pid getClassId() {
        return klass;
    }

    @Override
    protected int pssMarshallSize() {
        return 3;
    }

    @Override
    public void pssMarshall(Object[] attribs) {
        if (attribs.length >= 3) {
            attribs[0] = pssId;
            attribs[1] = klass;
            attribs[2] = testField;
        }
    }

    @Override
    public void pssUnmarshall(Object[] attribs) {
        if (attribs.length >= 3) {
            // pssId is handled by parent class
            klass = (pid)attribs[1];
            testField = (String)attribs[2];
        }
    }

    public String getTestField() {
        return testField;
    }

    public void setTestField(String testField) {
        this.testField = testField;
    }

    // Método de teste estático
    public static void main(String[] args) {
        try {
            System.out.println("Testando carregamento de classes PSS...");
            
            // Teste 1: Carregar classe StorageObjectBase
            Class.forName("com.visionnaire.PSS.client.StorageObjectBase");
            System.out.println("✅ StorageObjectBase carregado com sucesso");
            
            // Teste 2: Carregar classe pid
            Class.forName("com.visionnaire.PSS.pid");
            System.out.println("✅ pid carregado com sucesso");
            
            // Teste 3: Instanciar TestPSS
            TestPSS test = new TestPSS();
            test.setTestField("Teste Java 21");
            System.out.println("✅ TestPSS instanciado com sucesso");
            System.out.println("✅ Campo de teste: " + test.getTestField());
            
            // Teste 4: Testar serialização
            Object[] attribs = new Object[3];
            test.pssMarshall(attribs);
            System.out.println("✅ pssMarshall executado com sucesso");
            
            TestPSS test2 = new TestPSS();
            test2.pssUnmarshall(attribs);
            System.out.println("✅ pssUnmarshall executado com sucesso");
            
            System.out.println("");
            System.out.println("🎉 TODOS OS TESTES BÁSICOS PASSARAM!");
            System.out.println("✅ PSS parece compatível com Java 21");
            
        } catch (ClassNotFoundException e) {
            System.out.println("❌ Erro ao carregar classe PSS: " + e.getMessage());
            System.exit(1);
        } catch (Exception e) {
            System.out.println("❌ Erro durante teste: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}
"@

# Criar estrutura de diretório para package
$testPackageDir = "test"
if (-not (Test-Path $testPackageDir)) {
    Write-Host "Criando diretório: $testPackageDir" -ForegroundColor Gray
    New-Item -ItemType Directory -Path $testPackageDir -Force | Out-Null
}

# Verificar se diretório foi criado
if (-not (Test-Path $testPackageDir)) {
    Write-Host "❌ Falha ao criar diretório $testPackageDir" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Criar arquivo Java sem BOM
$javaFilePath = Join-Path $testPackageDir "TestPSS.java"
Write-Host "Caminho do arquivo: $javaFilePath" -ForegroundColor Gray
try {
    [System.IO.File]::WriteAllText($javaFilePath, $javaCode, [System.Text.UTF8Encoding]::new($false))
    Write-Host "✅ Arquivo TestPSS.java criado em: $javaFilePath" -ForegroundColor Green
} catch {
    Write-Host "❌ Erro ao criar TestPSS.java: $($_.Exception.Message)" -ForegroundColor Red
    # Fallback para método alternativo
    try {
        $javaCode | Out-File -FilePath $javaFilePath -Encoding ASCII
        Write-Host "⚠️ Usando encoding ASCII como fallback" -ForegroundColor Yellow
    } catch {
        Write-Host "❌ Falha total ao criar arquivo Java" -ForegroundColor Red
        Set-Location ..
        exit 1
    }
}

# Verificar se arquivo foi criado
if (-not (Test-Path $javaFilePath)) {
    Write-Host "❌ Arquivo TestPSS.java não foi criado em: $javaFilePath" -ForegroundColor Red
    Write-Host "📁 Diretório atual: $(Get-Location)" -ForegroundColor Gray
    Write-Host "📁 Conteúdo do diretório:" -ForegroundColor Gray
    Get-ChildItem | ForEach-Object { Write-Host "   $($_.Name)" -ForegroundColor Gray }
    Set-Location ..
    exit 1
} else {
    Write-Host "✅ Arquivo verificado: TestPSS.java existe" -ForegroundColor Green
}

# Teste de compilação
Write-Host ""
Write-Host "=== TESTE DE COMPILAÇÃO ===" -ForegroundColor Cyan
$classpath = "..\Source\WEB-INF\lib\*"

try {
    $compileResult = javac -cp $classpath test\TestPSS.java 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ COMPILAÇÃO: SUCESSO" -ForegroundColor Green
        $compileOK = $true
    } else {
        Write-Host "❌ COMPILAÇÃO: FALHOU" -ForegroundColor Red
        Write-Host "Erros de compilação:" -ForegroundColor Red
        Write-Host $compileResult -ForegroundColor Red
        $compileOK = $false
    }
} catch {
    Write-Host "❌ Erro ao executar javac: $($_.Exception.Message)" -ForegroundColor Red
    $compileOK = $false
}

# Teste de execução
Write-Host ""
Write-Host "=== TESTE DE EXECUÇÃO ===" -ForegroundColor Cyan
if ($compileOK) {
    try {
        $executeResult = java -cp "$classpath;." test.TestPSS 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ EXECUÇÃO: SUCESSO" -ForegroundColor Green
            Write-Host "Saída do programa:" -ForegroundColor Cyan
            Write-Host $executeResult -ForegroundColor White

            # Verificar se teste básico passou
            if ($executeResult -match "TODOS OS TESTES.*PASSARAM") {
                Write-Host ""
                Write-Host "🎉 TESTE BÁSICO PSS: APROVADO!" -ForegroundColor Green
                Write-Host "✅ PSS Core funciona com Java 21" -ForegroundColor Green
            }
        } else {
            Write-Host "❌ EXECUÇÃO: FALHOU" -ForegroundColor Red
            Write-Host "Erros de execução:" -ForegroundColor Red
            Write-Host $executeResult -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Erro ao executar java: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⏭️ Pulando teste de execução (compilação falhou)" -ForegroundColor Yellow
}

# Análise de dependências internas
Write-Host ""
Write-Host "=== ANÁLISE DE DEPENDÊNCIAS INTERNAS ===" -ForegroundColor Cyan
try {
    $jdepsResult = jdeps --jdk-internals "..\Source\WEB-INF\lib\pss.jar" 2>&1
    if ($jdepsResult -match "Warning|JDK internal") {
        Write-Host "⚠️ PSS usa APIs internas do JDK:" -ForegroundColor Yellow
        Write-Host $jdepsResult -ForegroundColor Yellow
    } else {
        Write-Host "✅ Nenhuma API interna crítica detectada" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ Não foi possível executar jdeps" -ForegroundColor Yellow
}

# Análise de módulos
Write-Host ""
Write-Host "=== ANÁLISE DE MÓDULOS NECESSÁRIOS ===" -ForegroundColor Cyan
try {
    $modulesResult = jdeps --print-module-deps "..\Source\WEB-INF\lib\pss.jar" 2>&1
    if ($modulesResult -match "Error: Missing dependencies") {
        Write-Host "⚠️ DEPENDÊNCIAS AUSENTES DETECTADAS:" -ForegroundColor Yellow
        Write-Host ""

        # Extrair dependências ausentes
        $missingDeps = @()
        $lines = $modulesResult -split "`n"
        foreach ($line in $lines) {
            if ($line -match "-> (.+)\s+not found") {
                $dep = $matches[1].Trim()
                if ($missingDeps -notcontains $dep) {
                    $missingDeps += $dep
                }
            }
        }

        Write-Host "📋 BIBLIOTECAS AUSENTES IDENTIFICADAS:" -ForegroundColor Cyan
        foreach ($dep in $missingDeps | Sort-Object) {
            Write-Host "   ❌ $dep" -ForegroundColor Red
        }

        Write-Host ""
        Write-Host "📊 ANÁLISE DE DEPENDÊNCIAS:" -ForegroundColor Cyan
        Write-Host "   • Total de dependências ausentes: $($missingDeps.Count)" -ForegroundColor White
        Write-Host "   • Principais categorias:" -ForegroundColor White

        $logDeps = $missingDeps | Where-Object { $_ -like "*log4j*" }
        $utilDeps = $missingDeps | Where-Object { $_ -like "*util*" }
        $jdbcDeps = $missingDeps | Where-Object { $_ -like "*jdbc*" }
        $servletDeps = $missingDeps | Where-Object { $_ -like "*servlet*" }

        if ($logDeps.Count -gt 0) { Write-Host "     - Log4j: $($logDeps.Count) dependências" -ForegroundColor Yellow }
        if ($utilDeps.Count -gt 0) { Write-Host "     - Utilities: $($utilDeps.Count) dependências" -ForegroundColor Yellow }
        if ($jdbcDeps.Count -gt 0) { Write-Host "     - JDBC: $($jdbcDeps.Count) dependências" -ForegroundColor Yellow }
        if ($servletDeps.Count -gt 0) { Write-Host "     - Servlet: $($servletDeps.Count) dependências" -ForegroundColor Yellow }

    } elseif ($modulesResult) {
        Write-Host "📦 Módulos necessários:" -ForegroundColor Cyan
        Write-Host $modulesResult -ForegroundColor White
    }
} catch {
    Write-Host "⚠️ Não foi possível analisar módulos" -ForegroundColor Yellow
}

# Limpeza
Set-Location ..
Remove-Item $testDir -Recurse -Force

# Resultado final
Write-Host ""
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "   RESULTADO DO TESTE" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow

# Verificar se execução foi bem-sucedida
$executeOK = $false
if ($compileOK) {
    try {
        $executeResult = java -cp "$classpath;." test.TestPSS 2>&1
        if ($LASTEXITCODE -eq 0 -and $executeResult -match "TODOS OS TESTES.*PASSARAM") {
            $executeOK = $true
        }
    } catch {
        $executeOK = $false
    }
}

if ($compileOK -and $executeOK) {
    Write-Host "🎉 RESULTADO: PSS COMPATÍVEL COM JAVA 21" -ForegroundColor Green
    Write-Host ""
    Write-Host "✅ Compilação bem-sucedida" -ForegroundColor Green
    Write-Host "✅ Execução bem-sucedida" -ForegroundColor Green
    Write-Host "✅ Classes PSS carregadas corretamente" -ForegroundColor Green
    Write-Host "✅ Operações básicas PSS funcionando" -ForegroundColor Green
    Write-Host ""
    Write-Host "⚠️ OBSERVAÇÕES IMPORTANTES:" -ForegroundColor Yellow
    Write-Host "• Dependências ausentes detectadas pelo jdeps" -ForegroundColor White
    Write-Host "• Essas dependências podem ser necessárias para funcionalidades avançadas" -ForegroundColor White
    Write-Host "• Testes básicos passaram, mas testes completos são recomendados" -ForegroundColor White
    Write-Host ""
    Write-Host "📋 PRÓXIMOS PASSOS RECOMENDADOS:" -ForegroundColor Cyan
    Write-Host "1. ✅ Prosseguir com migração Java 21 (risco baixo)" -ForegroundColor White
    Write-Host "2. 🧪 Executar testes mais extensivos da aplicação" -ForegroundColor White
    Write-Host "3. 🔧 Resolver dependências ausentes se necessário" -ForegroundColor White
    Write-Host "4. 🗄️ Testar com banco de dados real" -ForegroundColor White
    Write-Host "5. 📊 Monitorar logs durante uso em produção" -ForegroundColor White
} else {
    Write-Host "❌ RESULTADO: PSS TEM PROBLEMAS DE COMPATIBILIDADE" -ForegroundColor Red
    Write-Host ""
    Write-Host "� PROBLEMAS IDENTIFICADOS:" -ForegroundColor Cyan
    Write-Host "   • Dependências ausentes (log4j, util, jdbc, servlet)" -ForegroundColor White
    Write-Host "   • Possível incompatibilidade com Java 21" -ForegroundColor White
    Write-Host "   • Framework PSS pode precisar de atualização" -ForegroundColor White
    Write-Host ""
    Write-Host "�📞 AÇÕES NECESSÁRIAS (PRIORITÁRIAS):" -ForegroundColor Cyan
    Write-Host "1. 🔧 Adicionar bibliotecas ausentes ao classpath:" -ForegroundColor White
    Write-Host "   - Verificar se existem JARs faltando em WEB-INF/lib" -ForegroundColor Gray
    Write-Host "   - Procurar por: com.visionnaire.util.*, com.visionnaire.jdbc.*" -ForegroundColor Gray
    Write-Host "2. 📞 Contatar Visionnaire sobre:" -ForegroundColor White
    Write-Host "   - Compatibilidade PSS com Java 21" -ForegroundColor Gray
    Write-Host "   - Lista completa de dependências necessárias" -ForegroundColor Gray
    Write-Host "   - Versão atualizada do framework PSS" -ForegroundColor Gray
    Write-Host "3. 🔄 Verificar configuração do ambiente:" -ForegroundColor White
    Write-Host "   - Validar ant.properties (pss.home, vcomp.lib)" -ForegroundColor Gray
    Write-Host "   - Executar 'ant RefreshDepends' para atualizar libs" -ForegroundColor Gray
    Write-Host "4. ⏸️ Considerar adiar migração até resolver dependências" -ForegroundColor White
}

Write-Host ""
Write-Host "📄 Documentação completa: docs/migration/PSS_COMPATIBILITY_ANALYSIS.md" -ForegroundColor Cyan
Write-Host ""
Read-Host "Pressione Enter para continuar"
